import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, MapPin, Store, Package, RotateCcw,
  Truck, X, Loader, Sparkles, Zap,
  ChevronRight, Bot, Navigation,
  Shield, Crown, Gem
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { useAuth } from '../../contexts/AuthContext';
import Logo from '../../components/common/Logo';
import { apiService, comprehensiveSearch, type Service, type ComprehensiveSearchResults } from '../../services/api';

// Display service interface for UI components
interface DisplayService {
  key: string;
  label: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  color: string;
  bgGradient: string;
  shadowColor: string;
  route: string;
  badge: string;
  badgeColor: string;
  special?: boolean;
}

const HomePage: React.FC = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
  const [isAIChatModalVisible, setIsAIChatModalVisible] = useState(false);
  const [services, setServices] = useState<Service[]>([]);
  const [displayServices, setDisplayServices] = useState<DisplayService[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Show sliding header after scrolling past the hero section
        setIsHeaderCompact(currentScrollY > 300);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Convert mobile routes to web routes
  const convertMobileRouteToWeb = (mobileRoute: string): string => {
    // Route mapping from mobile to web format
    const routeMap: { [key: string]: string } = {
      'home/supplier-categories': '/customer/supplier-categories',
      'home/send-package': '/customer/send-package',
      '/home/<USER>': '/customer/request-pickup',
      '/ai-chat': '/ai-chat', // This one stays the same
      '/orders': '/customer/orders',
      '/packages': '/customer/packages'
    };

    return routeMap[mobileRoute] || mobileRoute;
  };

  // Load services data from API and merge with display data
  useEffect(() => {
    const loadServices = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiService.getServices();
        if (response.success && response.data) {
          setServices(response.data);
          // Merge API services with display services data
          const mergedServices = staticDisplayServicesData.map(displayService => {
            const apiService = response.data!.find(s => s.key === displayService.key);
            return {
              ...displayService,
              // Override with API data if available, converting mobile routes to web routes
              ...(apiService && {
                label: apiService.label,
                route: convertMobileRouteToWeb(apiService.route),
                isActive: apiService.isActive
              })
            };
          }).filter(service => {
            // Only show active services
            const apiService = response.data!.find(s => s.key === service.key);
            return !apiService || apiService.isActive;
          });
          setDisplayServices(mergedServices);
        } else {
          throw new Error(response.message || 'Failed to load services');
        }
      } catch (error) {
        console.error('Error loading services:', error);
        setError(error instanceof Error ? error.message : 'Failed to load services');
        // Fallback to static display services data
        setDisplayServices(staticDisplayServicesData);
      } finally {
        setLoading(false);
      }
    };

    loadServices();
  }, []);

  // Static display services data with premium styling and features
  const staticDisplayServicesData: DisplayService[] = [
    {
      key: 'shopOrder',
      label: 'Order from Supplier',
      subtitle: 'Browse & order from local suppliers',
      icon: Store,
      color: '#FFFFFF',
      bgGradient: 'from-blue-500 via-blue-600 to-blue-700',
      shadowColor: 'shadow-blue-500/25',
      route: '/customer/supplier-categories',
      badge: 'Popular',
      badgeColor: 'bg-yellow-400 text-yellow-900',
    },
    {
      key: 'sendPackage',
      label: 'Send a Package',
      subtitle: 'Quick & reliable package delivery',
      icon: Package,
      color: '#FFFFFF',
      bgGradient: 'from-orange-500 via-orange-600 to-red-500',
      shadowColor: 'shadow-orange-500/25',
      route: '/customer/send-package',
      badge: 'Fast',
      badgeColor: 'bg-green-400 text-green-900',
    },
    {
      key: 'requestPickup',
      label: 'Request Pickup',
      subtitle: 'Schedule convenient pickups',
      icon: RotateCcw,
      color: '#FFFFFF',
      bgGradient: 'from-emerald-500 via-teal-600 to-cyan-600',
      shadowColor: 'shadow-emerald-500/25',
      route: '/customer/request-pickup',
      badge: 'Flexible',
      badgeColor: 'bg-blue-400 text-blue-900',
    },
    {
      key: 'customDelivery',
      label: 'AI Chat System',
      subtitle: 'Smart assistance powered by AI',
      icon: Bot,
      color: '#FFFFFF',
      bgGradient: 'from-purple-500 via-violet-600 to-purple-700',
      shadowColor: 'shadow-purple-500/25',
      route: '/ai-chat',
      badge: 'AI Powered',
      badgeColor: 'bg-purple-400 text-purple-900',
      special: true,
    },
    {
      key: 'trackOrders',
      label: 'Track My Orders',
      subtitle: 'Real-time order tracking',
      icon: Navigation,
      color: '#FFFFFF',
      bgGradient: 'from-indigo-500 via-blue-600 to-indigo-700',
      shadowColor: 'shadow-indigo-500/25',
      route: '/customer/orders',
      badge: 'Live',
      badgeColor: 'bg-red-400 text-red-900',
    },
    {
      key: 'trackPackages',
      label: 'Track My Packages',
      subtitle: 'Monitor package delivery status',
      icon: Truck,
      color: '#FFFFFF',
      bgGradient: 'from-green-500 via-emerald-600 to-green-700',
      shadowColor: 'shadow-green-500/25',
      route: '/customer/packages',
      badge: 'Secure',
      badgeColor: 'bg-emerald-400 text-emerald-900',
    },
  ];

  // Handle service press (matching mobile functionality)
  const handleServicePress = (key: string, route: string) => {
    if (key === 'customDelivery') {
      setIsAIChatModalVisible(true);
    } else {
      // Convert mobile route to web route and navigate
      const webRoute = convertMobileRouteToWeb(route);
      window.location.href = webRoute;
    }
  };

  // Search functionality
  useEffect(() => {
    const performSearch = async () => {
      if (!searchQuery.trim()) {
        setSearchResults(null);
        setShowSearchResults(false);
        return;
      }

      setIsSearching(true);
      try {
        // Use real API search
        const results = await comprehensiveSearch(searchQuery);
        setSearchResults(results);
        setShowSearchResults(true);
      } catch (error) {
        console.error('Search error:', error);
        // Fallback to local search on display services
        const localResults: ComprehensiveSearchResults = {
          services: displayServices
            .filter(s => s.label.toLowerCase().includes(searchQuery.toLowerCase()))
            .map(s => ({
              type: 'service' as const,
              id: s.key,
              title: s.label,
              subtitle: s.subtitle,
              description: s.subtitle,
              icon: s.icon.name,
              color: s.color,
              route: s.route,
              data: s
            })),
          categories: [],
          suppliers: [],
          products: [],
          total: 0
        };
        localResults.total = localResults.services.length;
        setSearchResults(localResults);
        setShowSearchResults(true);
      } finally {
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery, displayServices]);

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setShowSearchResults(false);
  };

  const filteredServices = useMemo(() => {
    if (showSearchResults) return [];
    return displayServices;
  }, [showSearchResults, displayServices]);

  const userName = user?.firstName && user?.lastName
    ? `${user.firstName} ${user.lastName}`
    : user?.email?.split('@')[0] || 'User';

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-emerald-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-orange-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Sliding Header with Scroll Animation - Only appears when scrolling */}
        <motion.div
          className="fixed left-0 right-0 z-50"
          initial={{ y: -100, opacity: 0 }}
          animate={{
            y: isHeaderCompact ? 0 : -100,
            opacity: isHeaderCompact ? 1 : 0,
          }}
          transition={{
            duration: 0.4,
            ease: "easeInOut",
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          style={{
            top: 0,
            pointerEvents: isHeaderCompact ? "auto" : "none"
          }}
        >
          <div
            className="bg-slate-900 border-b border-white/10 shadow-2xl"
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="cursor-pointer"
                      onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                      <Logo size="sm" />
                    </motion.div>
                    <div>
                      <p className="text-white/60 text-xs">Premium Delivery</p>
                    </div>
                  </div>

                  {/* Compact Search */}
                  <div className="flex-1 max-w-md mx-8">
                    <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:border-white/30 transition-all duration-200">
                      <div className="flex items-center gap-3 p-3">
                        <Search size={18} className="text-white/60" />
                        <input
                          type="text"
                          placeholder="Search services, suppliers..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                        />
                        {searchQuery.trim() && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={clearSearch}
                            className="p-1 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200"
                          >
                            <X size={16} className="text-white" />
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsAIChatModalVisible(true)}
                    className="p-3 bg-gradient-to-r from-purple-500 to-blue-600 rounded-xl hover:from-purple-600 hover:to-blue-700 transition-all duration-200 shadow-lg"
                  >
                    <Bot className="text-white" size={20} />
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="relative z-10 w-full">
          {/* Hero Section - Full Screen */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2, ease: "easeInOut" }}
            className="min-h-screen flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8"
          >
            <div className="text-center max-w-4xl mx-auto">
              {/* Logo and Brand */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-8"
              >
                <div className="flex items-center justify-center gap-6 mb-6">
                  <div className="relative">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                    >
                      <Logo size="xl" />
                    </motion.div>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="absolute -inset-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full opacity-20 blur-xl"
                    />
                  </div>
                  <div className="text-left">
                    <motion.h1
                      className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-2"
                      animate={{
                        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                      }}
                      transition={{ duration: 5, repeat: Infinity }}
                    >
                      BOLTALAB
                    </motion.h1>
                    <p className="text-white/70 text-xl font-medium">Premium Delivery Experience</p>
                  </div>
                </div>
              </motion.div>

              {/* Welcome Message */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mb-8"
              >
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Welcome back, <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">{userName}</span>
                  <motion.span
                    animate={{ rotate: [0, 14, -8, 14, -4, 10, 0] }}
                    transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 3 }}
                    className="inline-block ml-2"
                  >
                    👋
                  </motion.span>
                </h2>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="flex items-center justify-center gap-3 text-white/80 text-lg"
                >
                  <MapPin size={20} className="text-blue-400" />
                  <span className="font-medium">Nablus, Palestine</span>
                  <div className="flex items-center gap-2 ml-6">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 font-medium">Online</span>
                  </div>
                </motion.div>
              </motion.div>

              {/* Stats Row */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="grid grid-cols-3 gap-8 mb-12"
              >
                <motion.div
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">24/7</div>
                  <div className="text-white/70 text-lg">Available</div>
                </motion.div>
                <motion.div
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">Fast</div>
                  <div className="text-white/70 text-lg">Delivery</div>
                </motion.div>
                <motion.div
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">Secure</div>
                  <div className="text-white/70 text-lg">Service</div>
                </motion.div>
              </motion.div>

              {/* Scroll Indicator */}
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex flex-col items-center gap-2 text-white/60"
              >
                <span className="text-sm font-medium">Scroll to explore services</span>
                <ChevronRight className="rotate-90" size={20} />
              </motion.div>
            </div>
          </motion.div>

          {/* Content Section with proper spacing */}
          <div className="px-4 sm:px-6 lg:px-8 pb-20">
            {/* Premium Search Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1.2, ease: "easeInOut" }}
              className="max-w-4xl mx-auto mb-16"
            >
              <div className="relative">
                <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                  <div className="flex items-center gap-4 p-6">
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl"
                    >
                      <Search size={24} className="text-white" />
                    </motion.div>
                    <input
                      type="text"
                      placeholder="Search services, suppliers, or items…"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-lg font-medium"
                    />
                    {searchQuery.trim() && (
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={clearSearch}
                        className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                      >
                        <X size={20} className="text-white" />
                      </motion.button>
                    )}
                  </div>

                  {/* Search suggestions */}
                  {!searchQuery && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="px-6 pb-4"
                    >
                      <div className="flex flex-wrap gap-2">
                        {['Fast Delivery', 'AI Chat', 'Track Orders', 'Local Suppliers'].map((suggestion, index) => (
                          <motion.button
                            key={suggestion}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.1 * index }}
                            onClick={() => setSearchQuery(suggestion)}
                            className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-white/80 text-sm transition-all duration-200"
                          >
                            {suggestion}
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Content Area */}
            <div className="max-w-7xl mx-auto">
            {showSearchResults ? (
              // Premium Search Results
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
                className="space-y-8"
              >
                {isSearching ? (
                  <div className="flex flex-col items-center justify-center py-20">
                    <div className="relative">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-20 h-20 border-4 border-white/20 border-t-blue-400 rounded-full"
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                      >
                        <Sparkles className="text-blue-400" size={28} />
                      </motion.div>
                    </div>
                    <motion.p
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="text-white/90 mt-6 text-xl font-medium"
                    >
                      Searching the universe...
                    </motion.p>
                  </div>
                ) : searchResults && searchResults.total > 0 ? (
                  <div className="space-y-6">
                    <motion.h3
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-6 text-center"
                    >
                      Search Results
                    </motion.h3>
                    <div className="space-y-8">
                      {/* Services Results */}
                      {searchResults.services.length > 0 && (
                        <div>
                          <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                            <Package size={20} className="text-blue-400" />
                            Services ({searchResults.services.length})
                          </h4>
                          <div className="grid gap-4">
                            {searchResults.services.map((service: any, index: number) => {
                              const matchingService = displayServices.find((s: DisplayService) => s.key === service.id || s.key === service.data?.key);
                              const IconComponent = matchingService?.icon || Store;
                              return (
                                <motion.div
                                  key={service.id}
                                  initial={{ opacity: 0, x: -30 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: index * 0.1 }}
                                  whileHover={{ scale: 1.02, x: 10 }}
                                  onClick={() => handleServicePress(service.id, service.route)}
                                  className={`bg-gradient-to-r ${matchingService?.bgGradient || 'from-blue-600 to-blue-700'} backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 ${matchingService?.shadowColor || 'shadow-blue-500/25'} shadow-xl hover:shadow-2xl`}
                                >
                                  <div className="flex items-center gap-6">
                                    <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                      <IconComponent className="text-white" size={32} />
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="text-white font-bold text-xl mb-1">{service.title}</h4>
                                      <p className="text-white/80 mb-2">{service.subtitle || matchingService?.subtitle || 'Tap to access'}</p>
                                      {matchingService?.badge && (
                                        <span className={`inline-block px-3 py-1 ${matchingService.badgeColor} rounded-full text-xs font-bold`}>
                                          {matchingService.badge}
                                        </span>
                                      )}
                                    </div>
                                    <ChevronRight className="text-white/60" size={24} />
                                  </div>
                                </motion.div>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {/* Categories Results */}
                      {searchResults.categories.length > 0 && (
                        <div>
                          <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                            <Store size={20} className="text-green-400" />
                            Categories ({searchResults.categories.length})
                          </h4>
                          <div className="grid gap-4">
                            {searchResults.categories.map((category: any, index: number) => (
                              <motion.div
                                key={category.id}
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ scale: 1.02, x: 10 }}
                                onClick={() => window.location.href = convertMobileRouteToWeb(category.route)}
                                className="bg-gradient-to-r from-green-600 to-emerald-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-green-500/25 shadow-xl hover:shadow-2xl"
                              >
                                <div className="flex items-center gap-6">
                                  <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                    <Store className="text-white" size={32} />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-white font-bold text-xl mb-1">{category.title}</h4>
                                    <p className="text-white/80 mb-2">{category.description || 'Browse suppliers in this category'}</p>
                                    <span className="inline-block px-3 py-1 bg-green-400 text-green-900 rounded-full text-xs font-bold">
                                      Category
                                    </span>
                                  </div>
                                  <ChevronRight className="text-white/60" size={24} />
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Suppliers Results */}
                      {searchResults.suppliers.length > 0 && (
                        <div>
                          <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                            <Store size={20} className="text-orange-400" />
                            Suppliers ({searchResults.suppliers.length})
                          </h4>
                          <div className="grid gap-4">
                            {searchResults.suppliers.map((supplier: any, index: number) => (
                              <motion.div
                                key={supplier.id}
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ scale: 1.02, x: 10 }}
                                onClick={() => window.location.href = convertMobileRouteToWeb(supplier.route)}
                                className="bg-gradient-to-r from-orange-600 to-red-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-orange-500/25 shadow-xl hover:shadow-2xl"
                              >
                                <div className="flex items-center gap-6">
                                  <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                    <Store className="text-white" size={32} />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-white font-bold text-xl mb-1">{supplier.title}</h4>
                                    <p className="text-white/80 mb-2">{supplier.subtitle || supplier.description || 'View supplier details'}</p>
                                    <span className="inline-block px-3 py-1 bg-orange-400 text-orange-900 rounded-full text-xs font-bold">
                                      Supplier
                                    </span>
                                  </div>
                                  <ChevronRight className="text-white/60" size={24} />
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Products Results */}
                      {searchResults.products.length > 0 && (
                        <div>
                          <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                            <Package size={20} className="text-purple-400" />
                            Products ({searchResults.products.length})
                          </h4>
                          <div className="grid gap-4">
                            {searchResults.products.map((product: any, index: number) => (
                              <motion.div
                                key={product.id}
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ scale: 1.02, x: 10 }}
                                onClick={() => window.location.href = convertMobileRouteToWeb(product.route)}
                                className="bg-gradient-to-r from-purple-600 to-pink-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-purple-500/25 shadow-xl hover:shadow-2xl"
                              >
                                <div className="flex items-center gap-6">
                                  <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                    <Package className="text-white" size={32} />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-white font-bold text-xl mb-1">{product.title}</h4>
                                    <p className="text-white/80 mb-2">{product.subtitle || product.description || 'View product details'}</p>
                                    <span className="inline-block px-3 py-1 bg-purple-400 text-purple-900 rounded-full text-xs font-bold">
                                      Product
                                    </span>
                                  </div>
                                  <ChevronRight className="text-white/60" size={24} />
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      )}
                      </div
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-20">
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="p-6 bg-white/10 rounded-3xl mb-6"
                    >
                      <Search className="text-white/60" size={64} />
                    </motion.div>
                    <h3 className="text-white text-2xl font-bold mb-2">No results found</h3>
                    <p className="text-white/60 text-center max-w-md">
                      Try searching for something else or browse our premium services below
                    </p>
                  </div>
                )}
              </motion.div>
            ) : (

              // Premium Services Grid
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="space-y-8"
              >
                <div className="text-center mb-8">
                  <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-4xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-4"
                  >
                    Premium Services
                  </motion.h2>
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7 }}
                    className="text-white/70 text-lg max-w-2xl mx-auto"
                  >
                    Experience the future of delivery with our cutting-edge services designed for your convenience
                  </motion.p>
                </div>

                {loading ? (
                  <div className="flex justify-center py-16">
                    <Loader className="animate-spin text-white" size={32} />
                  </div>
                ) : error ? (
                  <div className="text-center py-16">
                    <div className="bg-red-500/20 backdrop-blur-xl rounded-2xl p-6 border border-red-500/30">
                      <p className="text-red-200 mb-4">{error}</p>
                      <Button
                        onClick={() => window.location.reload()}
                        variant="secondary"
                        className="bg-red-500 hover:bg-red-600 text-white border-0"
                      >
                        Retry
                      </Button>
                    </div>
                  </div>
                ) : filteredServices.length === 0 ? (
                  <div className="text-center py-16">
                    <Package className="mx-auto text-white/40 mb-4" size={48} />
                    <p className="text-white/60">No services available</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                    {filteredServices.map((service: DisplayService, index: number) => {
                      const Icon = service.icon;
                      return (
                        <motion.div
                          key={service.key}
                          initial={{ opacity: 0, scale: 0.8, y: 30 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          transition={{
                            duration: 0.8,
                            delay: index * 0.15,
                            type: "spring",
                            stiffness: 120
                          }}
                          whileHover={{
                            scale: 1.05,
                            y: -8,
                            transition: { duration: 0.3 }
                          }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleServicePress(service.key, service.route)}
                          className="group cursor-pointer"
                        >
                          {/* Premium Service Card */}
                          <div className={`relative rounded-3xl p-8 bg-gradient-to-br ${service.bgGradient} ${service.shadowColor} shadow-2xl hover:shadow-3xl transition-all duration-500 border border-white/10 overflow-hidden`}>
                            {/* Background Pattern */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
                            <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>

                            {/* Badge */}
                            {service.badge && (
                              <motion.div
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.3 + index * 0.1 }}
                                className={`absolute top-4 right-4 px-3 py-1 ${service.badgeColor} rounded-full text-xs font-bold`}
                              >
                                {service.badge}
                              </motion.div>
                            )}

                            {/* Special AI glow effect */}
                            {service.special && (
                              <motion.div
                                animate={{ opacity: [0.5, 1, 0.5] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-pink-400/20 to-purple-400/20 rounded-3xl"
                              />
                            )}

                            <div className="relative z-10">
                              {/* Icon */}
                              <motion.div
                                whileHover={{ rotate: 360 }}
                                transition={{ duration: 0.6 }}
                                className="flex justify-center mb-6"
                              >
                                <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                  <Icon
                                    className="group-hover:scale-110 transition-transform duration-300"
                                    size={40}
                                    style={{ color: service.color }}
                                  />
                                </div>
                              </motion.div>

                              {/* Content */}
                              <div className="text-center">
                                <h3 className="text-white font-bold text-xl mb-2 leading-tight">
                                  {service.label}
                                </h3>
                                <p className="text-white/80 text-sm mb-4 leading-relaxed">
                                  {service.subtitle}
                                </p>

                                {/* Action indicator */}
                                <motion.div
                                  whileHover={{ x: 5 }}
                                  className="flex items-center justify-center gap-2 text-white/60 text-sm"
                                >
                                  <span>Get Started</span>
                                  <ChevronRight size={16} />
                                </motion.div>
                              </div>

                              {/* Special effects for AI Chat */}
                              {service.special && (
                                <motion.div
                                  animate={{ scale: [1, 1.05, 1] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                  className="flex items-center justify-center gap-2 text-white/90 text-xs mt-4 bg-white/10 rounded-full py-2 px-4"
                                >
                                  <Sparkles size={14} className="animate-pulse" />
                                  <span className="font-medium">AI Powered</span>
                                  <Zap size={14} className="animate-pulse" />
                                </motion.div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </motion.div>
            )}

          </div>
        </div>

        {/* Floating Action Button for Quick Access */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 1, type: "spring", stiffness: 200 }}
          className="fixed bottom-8 right-8 z-40"
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsAIChatModalVisible(true)}
            className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full shadow-2xl flex items-center justify-center group"
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full opacity-75 blur-lg"
            />
            <Bot className="text-white relative z-10 group-hover:scale-110 transition-transform" size={28} />
          </motion.button>
        </motion.div>

        {/* Premium Features Banner */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="fixed bottom-8 left-8 z-30 max-w-sm"
        >
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
                <Crown className="text-white" size={20} />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Premium Member</p>
                <p className="text-white/70 text-xs">Enjoy exclusive benefits</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Premium AI Chat Modal */}
      <AnimatePresence>
        {isAIChatModalVisible && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-xl z-50 flex items-center justify-center p-4"
            onClick={() => setIsAIChatModalVisible(false)}
          >
            <motion.div
              initial={{ scale: 0.7, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.7, opacity: 0, y: 50 }}
              transition={{ type: "spring", stiffness: 300, damping: 25 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-gradient-to-br from-purple-900 via-blue-900 to-purple-900 rounded-3xl p-8 max-w-md w-full shadow-2xl border border-white/20 relative overflow-hidden"
            >
              {/* Background effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"
              />

              <div className="relative z-10 text-center">
                {/* AI Icon with glow */}
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                    boxShadow: [
                      '0 0 20px rgba(139, 92, 246, 0.3)',
                      '0 0 40px rgba(139, 92, 246, 0.6)',
                      '0 0 20px rgba(139, 92, 246, 0.3)'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                  <Bot className="text-white" size={40} />
                </motion.div>

                {/* Title with gradient */}
                <h3 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-3">
                  AI Chat System
                </h3>

                {/* Subtitle */}
                <p className="text-white/80 mb-6 leading-relaxed">
                  Experience the future of customer service with our advanced AI assistant.
                  Get instant, intelligent help 24/7.
                </p>

                {/* Features */}
                <div className="grid grid-cols-2 gap-4 mb-8">
                  <div className="text-center">
                    <Zap className="text-yellow-400 mx-auto mb-2" size={20} />
                    <p className="text-white/70 text-sm">Instant Response</p>
                  </div>
                  <div className="text-center">
                    <Shield className="text-green-400 mx-auto mb-2" size={20} />
                    <p className="text-white/70 text-sm">Secure & Private</p>
                  </div>
                  <div className="text-center">
                    <Crown className="text-purple-400 mx-auto mb-2" size={20} />
                    <p className="text-white/70 text-sm">Premium AI</p>
                  </div>
                  <div className="text-center">
                    <Gem className="text-blue-400 mx-auto mb-2" size={20} />
                    <p className="text-white/70 text-sm">Smart Solutions</p>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="space-y-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      setIsAIChatModalVisible(false);
                      window.location.href = '/ai-chat';
                    }}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Sparkles size={20} />
                      <span>Start AI Chat</span>
                      <ChevronRight size={20} />
                    </div>
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setIsAIChatModalVisible(false)}
                    className="w-full bg-white/10 hover:bg-white/20 text-white font-medium py-3 px-6 rounded-2xl transition-all duration-300 border border-white/20"
                  >
                    Maybe Later
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </>
  );
};

export default HomePage;